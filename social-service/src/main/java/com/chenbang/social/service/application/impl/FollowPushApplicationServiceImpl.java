package com.chenbang.social.service.application.impl;

import com.chenbang.social.api.dto.request.FollowPushConfigRequest;
import com.chenbang.social.api.dto.response.FollowPushConfigResponse;
import com.chenbang.social.api.enums.ContentTypeEnum;
import com.chenbang.social.api.enums.PushTypeEnum;
import com.chenbang.social.api.enums.TaskStatusEnum;
import com.chenbang.social.service.application.FollowPushApplicationService;
import com.chenbang.social.service.application.FeedApplicationService;
import com.chenbang.social.service.application.MessagePushApplicationService;
import com.chenbang.social.service.application.MonitoringApplicationService;
import com.chenbang.social.service.application.assembler.FollowPushConfigAssembler;
import com.chenbang.social.service.domain.entity.FollowPushConfig;
import com.chenbang.social.service.domain.entity.ContentPublishTask;
import com.chenbang.social.service.domain.service.FollowPushDomainService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;

/**
 * 关注推送应用服务实现
 * <AUTHOR>
 * @since 2024-07-14
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FollowPushApplicationServiceImpl implements FollowPushApplicationService {

    private final FollowPushDomainService followPushDomainService;
    private final FeedApplicationService feedApplicationService;
    private final MessagePushApplicationService messagePushApplicationService;
    private final MonitoringApplicationService monitoringApplicationService;
    private final FollowPushConfigAssembler followPushConfigAssembler;
    private final StringRedisTemplate stringRedisTemplate;

    @Autowired
    @Qualifier("followPushTaskExecutor")
    private Executor taskExecutor;

    // 分批处理大小
    private static final int BATCH_SIZE = 1000;

    // 最大并发任务数限制
    private static final int MAX_CONCURRENT_TASKS = 100;

    // 当前处理中的任务计数器（线程安全）
    private final java.util.concurrent.atomic.AtomicInteger currentTaskCount = new java.util.concurrent.atomic.AtomicInteger(0);

    // Redis分布式锁超时时间（毫秒）
    private static final long REDIS_LOCK_TIMEOUT_MS = 30000; // 30秒
    private static final long REDIS_LOCK_RETRY_INTERVAL_MS = 50; // 重试间隔50毫秒

    // 任务创建锁超时时间（毫秒）
    private static final long TASK_CREATION_LOCK_TIMEOUT_MS = 10000; // 10秒

    // 当前实例的唯一标识，用于分布式锁的锁值验证
    private final String instanceId = java.util.UUID.randomUUID().toString();

    // ThreadLocal存储当前线程的锁值，用于释放锁时验证
    private final ThreadLocal<String> lockValueThreadLocal = new ThreadLocal<>();
    
    @Override
    @Transactional
    public FollowPushConfigResponse configFollowPush(FollowPushConfigRequest request) {
        log.info("配置用户关注推送设置，userId: {}, pushType: {}, enabled: {}", 
                request.getUserId(), request.getPushType(), request.getEnabled());
        
        FollowPushConfig config = followPushDomainService.createOrUpdateConfig(
                request.getUserId(), 
                request.getPushType(), 
                request.getEnabled(), 
                getCurrenttenantCode(), 
                getCurrentOperator()
        );
        
        return followPushConfigAssembler.toResponse(config);
    }
    
    @Override
    public FollowPushConfigResponse getFollowPushConfig(Long userId) {
        log.info("获取用户关注推送配置，userId: {}", userId);
        
        FollowPushConfig config = followPushDomainService.getConfig(userId);
        return followPushConfigAssembler.toResponse(config);
    }
    
    @Override
    public void handleContentPublish(Long publisherId, Long contentId, ContentTypeEnum contentType,
                                   String contentTitle, String contentSummary, String contentCover, String contentUrl) {
        long startTime = System.currentTimeMillis();

        // 增强的参数验证
        validateParameters(publisherId, contentId, contentType, contentTitle, contentSummary, contentCover, contentUrl);

        log.info("处理内容发布事件，publisherId: {}, contentId: {}, contentType: {}",
                publisherId, contentId, contentType);

        // 生成唯一的处理任务ID，用于幂等性控制
        String taskId = generateTaskId(publisherId, contentId);

        // 预先占用并发资源，确保原子性
        boolean resourceAcquired = false;
        try {
            int currentTasks = currentTaskCount.incrementAndGet();
            if (currentTasks > MAX_CONCURRENT_TASKS) {
                // 超过限制，回退计数器
                currentTaskCount.decrementAndGet();
                log.warn("当前并发任务数已达上限，taskId: {}, currentTasks: {}, maxTasks: {}",
                        taskId, currentTasks - 1, MAX_CONCURRENT_TASKS);
                throw new RuntimeException("系统繁忙，当前并发任务数已达上限，请稍后重试");
            }
            resourceAcquired = true;
        } catch (Exception e) {
            log.error("检查并发限制时发生异常，taskId: {}", taskId, e);
            throw e;
        }

        // 使用分布式锁确保任务创建的原子性，避免竞态条件
        String lockKey = "content_publish_lock_" + taskId;
        boolean lockAcquired = false;
        boolean taskCreated = false;
        long lockStartTime = System.currentTimeMillis();

        try {
            // 尝试获取分布式锁，使用合理的超时时间
            lockAcquired = acquireDistributedLock(lockKey, TASK_CREATION_LOCK_TIMEOUT_MS);
            long lockAcquireTime = System.currentTimeMillis() - lockStartTime;

            if (!lockAcquired) {
                log.warn("获取分布式锁失败，可能存在并发处理，taskId: {}, 耗时: {}ms", taskId, lockAcquireTime);
                throw new RuntimeException("获取处理锁失败，请稍后重试");
            }

            log.debug("成功获取分布式锁，taskId: {}, 耗时: {}ms", taskId, lockAcquireTime);

            // 在锁保护下进行幂等性检查和任务创建
            ContentPublishTask existingTask = followPushDomainService.getTask(taskId);
            if (existingTask != null) {
                if (existingTask.isProcessed()) {
                    log.info("内容发布事件已处理完成，跳过处理，taskId: {}, status: {}",
                            taskId, existingTask.getTaskStatus());
                    return;
                } else if (existingTask.canRecover()) {
                    log.info("发现可恢复的任务，恢复处理，taskId: {}, status: {}",
                            taskId, existingTask.getTaskStatus());
                    // 恢复处理现有任务，不释放并发资源
                    recoverContentPublishTask(existingTask);
                    resourceAcquired = false; // 标记资源已被恢复任务使用
                    return;
                } else {
                    // 任务存在但无法恢复（如CANCELLED状态），记录警告并跳过
                    log.warn("任务已存在但无法恢复，跳过处理，taskId: {}, status: {}",
                            taskId, existingTask.getTaskStatus());
                    return;
                }
            }

            // 创建新的处理任务记录（原子操作）
            try {
                followPushDomainService.createProcessTask(taskId, publisherId, contentId, contentType,
                        contentTitle, contentSummary, contentCover, contentUrl);
                taskCreated = true;
            } catch (Exception createException) {
                log.error("创建任务失败，taskId: {}", taskId, createException);
                throw createException; // 重新抛出异常，让外层catch处理
            }

            // 异步分批处理，避免长事务和内存问题
            // 注意：不在这里调用processContentPublishAsync，因为它内部会管理计数器
            // 直接提交到线程池，让processContentPublishAsync管理资源
            submitAsyncTask(taskId, publisherId, contentId, contentType,
                    contentTitle, contentSummary, contentCover, contentUrl);

            // 任务已成功提交，不需要在这里减少计数器
            resourceAcquired = false;

        } catch (Exception e) {
            log.error("处理内容发布事件失败，publisherId: {}, contentId: {}, 耗时: {}ms",
                    publisherId, contentId, System.currentTimeMillis() - startTime, e);
            // 只有在任务已创建的情况下才标记失败
            if (taskCreated) {
                try {
                    followPushDomainService.markTaskFailed(taskId, e.getMessage());
                } catch (Exception markFailedException) {
                    log.error("标记任务失败时发生异常，taskId: {}", taskId, markFailedException);
                }
            }
            throw e;
        } finally {
            // 确保释放分布式锁
            if (lockAcquired) {
                try {
                    releaseDistributedLock(lockKey);
                } catch (Exception e) {
                    log.error("释放分布式锁失败，lockKey: {}", lockKey, e);
                }
            }

            // 如果资源已占用但任务未成功提交，需要释放资源
            if (resourceAcquired) {
                int remainingTasks = currentTaskCount.decrementAndGet();
                log.debug("释放未使用的并发资源，taskId: {}, 剩余任务数: {}", taskId, remainingTasks);
            }

            log.info("内容发布事件处理完成，taskId: {}, 总耗时: {}ms", taskId, System.currentTimeMillis() - startTime);
        }
    }

    /**
     * 提交异步任务
     */
    private void submitAsyncTask(String taskId, Long publisherId, Long contentId, ContentTypeEnum contentType,
                               String contentTitle, String contentSummary, String contentCover, String contentUrl) {
        try {
            taskExecutor.execute(() -> processContentPublishAsync(taskId, publisherId, contentId, contentType,
                    contentTitle, contentSummary, contentCover, contentUrl));
        } catch (Exception e) {
            log.error("提交异步任务失败，taskId: {}", taskId, e);
            // 提交失败时标记任务失败
            try {
                followPushDomainService.markTaskFailed(taskId, "线程池异常: " + e.getMessage());
            } catch (Exception markFailedException) {
                log.error("标记任务失败时发生异常，taskId: {}", taskId, markFailedException);
            }
            throw new RuntimeException("线程池异常，任务提交失败", e);
        }
    }

    /**
     * 异步分批处理内容发布
     */
    private void processContentPublishAsync(String taskId, Long publisherId, Long contentId, ContentTypeEnum contentType,
                                          String contentTitle, String contentSummary, String contentCover, String contentUrl) {
        // 使用线程池异步处理，避免阻塞消息消费
        Runnable task = () -> {
            // 注意：计数器已经在外层预先增加了，这里不需要再增加
            String currentThreadName = Thread.currentThread().getName();

            try {
                log.info("开始异步处理内容发布任务，taskId: {}, thread: {}, currentTasks: {}",
                        taskId, currentThreadName, currentTaskCount.get());

                // 在异步处理开始前再次检查任务状态，防止重复处理
                ContentPublishTask currentTask = followPushDomainService.getTask(taskId);
                if (currentTask == null) {
                    log.error("任务不存在，无法继续处理，taskId: {}", taskId);
                    return;
                }
                if (currentTask.isProcessed()) {
                    log.info("任务已被处理，跳过异步处理，taskId: {}", taskId);
                    return;
                }
                if (currentTask.isCancelled()) {
                    log.info("任务已被取消，跳过异步处理，taskId: {}", taskId);
                    return;
                }

                // 分批处理站内信推送
                processInAppMessagePush(taskId, publisherId, contentId, contentType, contentTitle);

                // 分批处理Feed流推送
                processFeedStreamPush(taskId, publisherId, contentId, contentType,
                        contentTitle, contentSummary, contentCover, contentUrl);

                // 检查并标记任务完成（统一的完成检查逻辑）
                checkAndMarkTaskCompleted(taskId);

                log.info("异步处理内容发布任务完成，taskId: {}, thread: {}", taskId, currentThreadName);

            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.error("异步处理内容发布事件被中断，taskId: {}, thread: {}", taskId, currentThreadName, e);
                try {
                    followPushDomainService.markTaskFailed(taskId, "处理被中断: " + e.getMessage());
                } catch (Exception markFailedException) {
                    log.error("标记任务失败时发生异常，taskId: {}", taskId, markFailedException);
                }
            } catch (Exception e) {
                log.error("异步处理内容发布事件失败，taskId: {}, thread: {}", taskId, currentThreadName, e);
                try {
                    followPushDomainService.markTaskFailed(taskId, e.getMessage());
                } catch (Exception markFailedException) {
                    log.error("标记任务失败时发生异常，taskId: {}", taskId, markFailedException);
                }
            } finally {
                // 确保减少并发任务计数，防止计数器泄漏
                int remainingTasks = currentTaskCount.decrementAndGet();
                log.debug("异步任务完成，taskId: {}, 剩余任务数: {}", taskId, remainingTasks);
            }
        };

        // 直接执行任务，因为并发控制已在外层处理
        if (taskExecutor != null) {
            taskExecutor.execute(task);
            log.debug("任务已提交到线程池，taskId: {}", taskId);
        } else {
            // 如果没有配置线程池，则同步执行（主要用于测试）
            log.warn("未配置线程池，使用同步执行，taskId: {}", taskId);
            task.run();
        }
    }

    /**
     * 分批处理站内信推送（支持断点恢复）
     */
    private void processInAppMessagePush(String taskId, Long publisherId, Long contentId,
                                       ContentTypeEnum contentType, String contentTitle) throws InterruptedException {
        log.info("开始分批处理站内信推送，taskId: {}, publisherId: {}", taskId, publisherId);

        // 获取任务信息，支持断点恢复
        ContentPublishTask task = followPushDomainService.getTask(taskId);
        if (task == null) {
            log.error("任务不存在，无法处理站内信推送，taskId: {}", taskId);
            return;
        }

        // 如果站内信推送已完成，直接返回
        if (task.isInAppMessageCompleted()) {
            log.info("站内信推送已完成，跳过处理，taskId: {}", taskId);
            return;
        }

        String messageTitle = "你关注的用户发布了新内容";
        String safeContentTitle = contentTitle != null ? contentTitle : "未知内容";
        String messageContent = String.format("用户发布了新的%s：%s", getContentTypeDesc(contentType), safeContentTitle);

        // 从上次中断的位置继续处理，添加边界检查
        int offset = task.getInAppMessageOffset() != null ? Math.max(0, task.getInAppMessageOffset()) : 0;
        int totalProcessed = task.getInAppMessageProgress() != null ? Math.max(0, task.getInAppMessageProgress()) : 0;

        // 验证数据一致性
        if (offset < 0 || totalProcessed < 0) {
            log.warn("检测到异常的任务进度数据，重置为0，taskId: {}, offset: {}, totalProcessed: {}",
                    taskId, offset, totalProcessed);
            offset = 0;
            totalProcessed = 0;
        }

        // 首次处理时计算总数（使用高效的count查询）
        if (task.getInAppMessageTotal() == null || task.getInAppMessageTotal() == 0) {
            int totalFollowersCount = followPushDomainService.getInAppMessageFollowersCount(publisherId);
            followPushDomainService.setTaskTotal(taskId, "IN_APP_MESSAGE", totalFollowersCount);
            log.info("设置站内信推送总数，taskId: {}, totalCount: {}", taskId, totalFollowersCount);
        }

        int maxIterations = 10000; // 防止无限循环的最大迭代次数
        int currentIteration = 0;
        int consecutiveErrors = 0; // 连续错误计数
        final int maxConsecutiveErrors = 5; // 最大连续错误次数

        while (currentIteration < maxIterations) {
            currentIteration++;

            // 分批获取关注者
            List<Long> followers = followPushDomainService.getInAppMessageFollowersBatch(publisherId, offset, BATCH_SIZE);

            if (followers.isEmpty()) {
                log.info("没有更多关注者需要处理，站内信推送结束，taskId: {}", taskId);
                break;
            }

            // 验证批次大小
            if (followers.size() > BATCH_SIZE) {
                log.warn("批次大小超出预期，taskId: {}, expected: {}, actual: {}",
                        taskId, BATCH_SIZE, followers.size());
                followers = followers.subList(0, BATCH_SIZE);
            }

            long batchStartTime = System.currentTimeMillis();
            try {
                // 检查任务是否已被取消
                if (followPushDomainService.isTaskCancelled(taskId)) {
                    log.info("任务已被取消，停止处理，taskId: {}", taskId);
                    break;
                }

                // 批量发送推送消息
                messagePushApplicationService.batchSendPushMessage(
                        followers, publisherId, contentId, PushTypeEnum.IN_APP_MESSAGE,
                        messageTitle, messageContent);

                totalProcessed += followers.size();
                offset += BATCH_SIZE;

                // 更新处理进度和偏移量
                followPushDomainService.updateTaskProgress(taskId, "IN_APP_MESSAGE", totalProcessed, offset);

                // 记录成功处理的监控信息
                long processingTime = System.currentTimeMillis() - batchStartTime;
                monitoringApplicationService.recordBatchSuccess(taskId, "IN_APP_MESSAGE", offset, followers.size(), processingTime);

                log.info("站内信推送批次处理完成，taskId: {}, 本批次: {}, 累计: {}, 耗时: {}ms",
                        taskId, followers.size(), totalProcessed, processingTime);

                // 重置连续错误计数
                consecutiveErrors = 0;

                // 避免过快处理导致系统压力过大
                try {
                    Thread.sleep(100);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    log.warn("任务处理被中断，taskId: {}", taskId);
                    throw e; // 中断异常需要向上抛出
                }

            } catch (InterruptedException e) {
                // 中断异常直接向上抛出
                throw e;
            } catch (Exception e) {
                consecutiveErrors++;
                log.error("站内信推送批次处理失败，taskId: {}, offset: {}, 连续错误次数: {}",
                        taskId, offset, consecutiveErrors, e);

                // 记录失败的批次
                try {
                    followPushDomainService.recordBatchError(taskId, "IN_APP_MESSAGE", offset, e.getMessage());
                    monitoringApplicationService.recordBatchFailure(taskId, "IN_APP_MESSAGE", offset, e.getMessage(), followers.size());
                } catch (Exception recordError) {
                    log.error("记录批次错误失败，taskId: {}", taskId, recordError);
                }

                // 检查连续错误次数
                if (consecutiveErrors >= maxConsecutiveErrors) {
                    log.error("连续错误次数达到上限，停止处理，taskId: {}, consecutiveErrors: {}",
                            taskId, consecutiveErrors);
                    throw new RuntimeException("连续错误次数过多，停止处理", e);
                }

                // 根据错误类型决定是否继续处理
                if (shouldStopOnError(e)) {
                    log.error("遇到严重错误，停止处理，taskId: {}", taskId);
                    throw e;
                } else {
                    // 非严重错误，跳过当前批次继续处理
                    log.warn("跳过当前批次继续处理，taskId: {}, offset: {}", taskId, offset);
                    offset += BATCH_SIZE;
                    // 不更新totalProcessed，因为这批次实际失败了
                    // 安全地更新进度，如果更新失败也不影响主流程
                    try {
                        followPushDomainService.updateTaskProgress(taskId, "IN_APP_MESSAGE", totalProcessed, offset);
                    } catch (Exception updateException) {
                        log.error("更新任务进度失败，但继续处理，taskId: {}, offset: {}", taskId, offset, updateException);
                        // 不抛出异常，继续处理下一批次
                    }

                    // 错误后稍微延长等待时间
                    try {
                        Thread.sleep(500);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        throw ie;
                    }
                }
            }
        }

        // 检查是否因为达到最大迭代次数而退出
        if (currentIteration >= maxIterations) {
            log.error("站内信推送处理达到最大迭代次数，可能存在无限循环，taskId: {}", taskId);
            followPushDomainService.markTaskFailed(taskId, "处理达到最大迭代次数，可能存在无限循环");
            throw new RuntimeException("处理超时，达到最大迭代次数");
        }

        // 标记站内信推送完成
        followPushDomainService.markInAppMessageCompleted(taskId);
        log.info("站内信推送处理完成，taskId: {}, 总计处理: {}", taskId, totalProcessed);
    }

    /**
     * 分批处理Feed流推送（支持断点恢复）
     */
    private void processFeedStreamPush(String taskId, Long publisherId, Long contentId, ContentTypeEnum contentType,
                                     String contentTitle, String contentSummary, String contentCover, String contentUrl) throws InterruptedException {
        log.info("开始分批处理Feed流推送，taskId: {}, publisherId: {}", taskId, publisherId);

        // 获取任务信息，支持断点恢复
        ContentPublishTask task = followPushDomainService.getTask(taskId);
        if (task == null) {
            log.error("任务不存在，无法处理Feed流推送，taskId: {}", taskId);
            return;
        }

        // 如果Feed流推送已完成，直接返回
        if (task.isFeedStreamCompleted()) {
            log.info("Feed流推送已完成，跳过处理，taskId: {}", taskId);
            return;
        }

        // 从上次中断的位置继续处理，添加边界检查
        int offset = task.getFeedStreamOffset() != null ? Math.max(0, task.getFeedStreamOffset()) : 0;
        int totalProcessed = task.getFeedStreamProgress() != null ? Math.max(0, task.getFeedStreamProgress()) : 0;

        // 验证数据一致性
        if (offset < 0 || totalProcessed < 0) {
            log.warn("检测到异常的Feed流任务进度数据，重置为0，taskId: {}, offset: {}, totalProcessed: {}",
                    taskId, offset, totalProcessed);
            offset = 0;
            totalProcessed = 0;
        }

        // 首次处理时计算总数（使用高效的count查询）
        if (task.getFeedStreamTotal() == null || task.getFeedStreamTotal() == 0) {
            int totalFollowersCount = followPushDomainService.getFeedStreamFollowersCount(publisherId);
            followPushDomainService.setTaskTotal(taskId, "FEED_STREAM", totalFollowersCount);
            log.info("设置Feed流推送总数，taskId: {}, totalCount: {}", taskId, totalFollowersCount);
        }

        int maxIterations = 10000; // 防止无限循环的最大迭代次数
        int currentIteration = 0;

        while (currentIteration < maxIterations) {
            currentIteration++;

            // 分批获取关注者
            List<Long> followers = followPushDomainService.getFeedStreamFollowersBatch(publisherId, offset, BATCH_SIZE);

            if (followers.isEmpty()) {
                break;
            }

            long batchStartTime = System.currentTimeMillis();
            try {
                // 检查任务是否已被取消
                if (followPushDomainService.isTaskCancelled(taskId)) {
                    log.info("任务已被取消，停止处理，taskId: {}", taskId);
                    break;
                }

                // 批量添加Feed流记录
                feedApplicationService.batchAddUserFeed(
                        followers, publisherId, contentId, contentType,
                        contentTitle, contentSummary, contentCover, contentUrl);

                totalProcessed += followers.size();
                offset += BATCH_SIZE;

                // 更新处理进度和偏移量
                followPushDomainService.updateTaskProgress(taskId, "FEED_STREAM", totalProcessed, offset);

                // 记录成功处理的监控信息
                long processingTime = System.currentTimeMillis() - batchStartTime;
                monitoringApplicationService.recordBatchSuccess(taskId, "FEED_STREAM", offset, followers.size(), processingTime);

                log.info("Feed流推送批次处理完成，taskId: {}, 本批次: {}, 累计: {}, 耗时: {}ms",
                        taskId, followers.size(), totalProcessed, processingTime);

                // 避免过快处理导致系统压力过大
                try {
                    Thread.sleep(100);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    log.warn("任务处理被中断，taskId: {}", taskId);
                    throw e; // 中断异常需要向上抛出
                }

            } catch (InterruptedException e) {
                // 中断异常直接向上抛出
                throw e;
            } catch (Exception e) {
                log.error("Feed流推送批次处理失败，taskId: {}, offset: {}", taskId, offset, e);

                // 安全地记录失败的批次，但继续处理下一批次
                try {
                    followPushDomainService.recordBatchError(taskId, "FEED_STREAM", offset, e.getMessage());
                    monitoringApplicationService.recordBatchFailure(taskId, "FEED_STREAM", offset, e.getMessage(), followers.size());
                } catch (Exception recordException) {
                    log.error("记录批次错误失败，但继续处理，taskId: {}, offset: {}", taskId, offset, recordException);
                }

                // 继续下一批次，更新偏移量和进度（即使失败也要统计）
                totalProcessed += followers.size();
                offset += BATCH_SIZE;
                // 安全地更新进度，标记这批数据已处理（虽然失败了）
                try {
                    followPushDomainService.updateTaskProgress(taskId, "FEED_STREAM", totalProcessed, offset);
                } catch (Exception updateException) {
                    log.error("更新任务进度失败，但继续处理，taskId: {}, offset: {}", taskId, offset, updateException);
                    // 不抛出异常，继续处理下一批次
                }
            }
        }

        // 检查是否因为达到最大迭代次数而退出
        if (currentIteration >= maxIterations) {
            log.error("Feed流推送处理达到最大迭代次数，可能存在无限循环，taskId: {}", taskId);
            followPushDomainService.markTaskFailed(taskId, "处理达到最大迭代次数，可能存在无限循环");
            return;
        }

        // 标记Feed流推送完成
        followPushDomainService.markFeedStreamCompleted(taskId);
        log.info("Feed流推送处理完成，taskId: {}, 总计处理: {}", taskId, totalProcessed);
    }

    /**
     * 恢复处理现有任务
     */
    private void recoverContentPublishTask(ContentPublishTask task) {
        log.info("恢复处理任务，taskId: {}, status: {}", task.getTaskId(), task.getTaskStatus());

        // 异步恢复处理
        Runnable recoveryTask = () -> {
            // 注意：计数器已经在外层预先增加了，这里不需要再增加

            try {
                // 检查任务状态是否为空
                TaskStatusEnum taskStatus = task.getTaskStatus();
                if (taskStatus == null) {
                    log.warn("任务状态为空，无法恢复，taskId: {}", task.getTaskId());
                    return;
                }

                // 根据任务状态决定恢复策略
                switch (taskStatus) {
                    case PROCESSING:
                        // 两个推送都需要处理
                        processInAppMessagePush(task.getTaskId(), task.getPublisherId(), task.getContentId(),
                                task.getContentType(), task.getContentTitle());
                        processFeedStreamPush(task.getTaskId(), task.getPublisherId(), task.getContentId(),
                                task.getContentType(), task.getContentTitle(), task.getContentSummary(),
                                task.getContentCover(), task.getContentUrl());
                        break;
                    case IN_APP_COMPLETED:
                        // 只需要处理Feed流推送
                        processFeedStreamPush(task.getTaskId(), task.getPublisherId(), task.getContentId(),
                                task.getContentType(), task.getContentTitle(), task.getContentSummary(),
                                task.getContentCover(), task.getContentUrl());
                        break;
                    case FEED_COMPLETED:
                        // 只需要处理站内信推送
                        processInAppMessagePush(task.getTaskId(), task.getPublisherId(), task.getContentId(),
                                task.getContentType(), task.getContentTitle());
                        break;
                    default:
                        log.warn("无法恢复的任务状态，taskId: {}, status: {}", task.getTaskId(), taskStatus);
                        return;
                }

                // 检查并标记任务完成（使用统一的完成检查逻辑）
                checkAndMarkTaskCompleted(task.getTaskId());

            } catch (Exception e) {
                log.error("恢复处理任务失败，taskId: {}", task.getTaskId(), e);
                try {
                    followPushDomainService.markTaskFailed(task.getTaskId(), e.getMessage());
                } catch (Exception markFailedException) {
                    log.error("标记恢复任务失败时发生异常，taskId: {}", task.getTaskId(), markFailedException);
                }
            } finally {
                // 确保减少并发任务计数，防止计数器泄漏
                int remainingTasks = currentTaskCount.decrementAndGet();
                log.debug("恢复任务完成，taskId: {}, 剩余任务数: {}", task.getTaskId(), remainingTasks);
            }
        };

        // 直接提交恢复任务，因为并发控制应该在调用此方法前处理
        if (taskExecutor != null) {
            try {
                taskExecutor.execute(recoveryTask);
                log.debug("恢复任务已提交到线程池，taskId: {}", task.getTaskId());
            } catch (Exception e) {
                log.error("提交恢复任务到线程池失败，taskId: {}", task.getTaskId(), e);
                // 线程池异常时，减少计数器并标记任务失败
                currentTaskCount.decrementAndGet();
                try {
                    followPushDomainService.markTaskFailed(task.getTaskId(), "线程池异常，无法恢复: " + e.getMessage());
                } catch (Exception markFailedException) {
                    log.error("标记恢复任务失败时发生异常，taskId: {}", task.getTaskId(), markFailedException);
                }
                throw new RuntimeException("恢复任务提交失败", e);
            }
        } else {
            // 如果没有配置线程池，则同步执行（主要用于测试）
            log.warn("未配置线程池，使用同步执行恢复任务，taskId: {}", task.getTaskId());
            recoveryTask.run();
        }
    }

    /**
     * 检查并标记任务完成
     * 统一的任务完成检查逻辑，避免重复代码和竞态条件
     */
    private void checkAndMarkTaskCompleted(String taskId) {
        // 使用分布式锁确保任务完成检查的原子性
        String lockKey = "task_completion_lock_" + taskId;
        boolean lockAcquired = false;

        try {
            lockAcquired = acquireDistributedLock(lockKey, 3000);
            if (!lockAcquired) {
                log.warn("获取任务完成检查锁失败，taskId: {}", taskId);
                // 不能直接返回，因为这可能导致任务永远不会被标记为完成
                // 尝试无锁检查，虽然可能有竞态条件，但比不检查要好
                log.info("尝试无锁检查任务完成状态，taskId: {}", taskId);
                try {
                    ContentPublishTask task = followPushDomainService.getTask(taskId);
                    if (task != null && task.isInAppMessageCompleted() && task.isFeedStreamCompleted()
                        && task.getTaskStatus() != TaskStatusEnum.COMPLETED) {
                        followPushDomainService.markTaskCompleted(taskId);
                        log.info("无锁模式下标记任务完成，taskId: {}", taskId);
                    }
                } catch (Exception e) {
                    log.error("无锁检查任务完成状态失败，taskId: {}", taskId, e);
                }
                return;
            }

            ContentPublishTask task = followPushDomainService.getTask(taskId);
            if (task != null && task.isInAppMessageCompleted() && task.isFeedStreamCompleted()) {
                // 再次检查任务状态，防止重复标记
                if (task.getTaskStatus() != TaskStatusEnum.COMPLETED) {
                    followPushDomainService.markTaskCompleted(taskId);
                    log.info("任务已全部完成，taskId: {}", taskId);
                } else {
                    log.debug("任务已经是完成状态，跳过标记，taskId: {}", taskId);
                }
            }
        } catch (Exception e) {
            log.error("检查任务完成状态时发生异常，taskId: {}", taskId, e);
        } finally {
            if (lockAcquired) {
                try {
                    releaseDistributedLock(lockKey);
                } catch (Exception e) {
                    log.error("释放任务完成检查锁失败，lockKey: {}", lockKey, e);
                }
            }
        }
    }

    /**
     * 判断是否应该因为错误而停止处理
     */
    private boolean shouldStopOnError(Exception e) {
        // 数据库连接错误、网络超时等严重错误应该停止处理
        if (e instanceof java.sql.SQLException) {
            return true;
        }
        if (e instanceof java.net.SocketTimeoutException) {
            return true;
        }
        if (e instanceof InterruptedException) {
            return true;
        }

        // 检查是否是内存错误（通过异常信息判断）
        String errorMessage = e.getMessage();
        if (errorMessage != null) {
            if (errorMessage.contains("OutOfMemoryError") ||
                errorMessage.contains("内存不足") ||
                errorMessage.contains("Java heap space")) {
                return true;
            }

            // 业务逻辑错误可以继续处理下一批次
            if (errorMessage.contains("用户不存在") ||
                errorMessage.contains("权限不足") ||
                errorMessage.contains("参数错误")) {
                return false;
            }
        }

        // 默认情况下继续处理
        return false;
    }

    /**
     * 验证输入参数
     */
    private void validateParameters(Long publisherId, Long contentId, ContentTypeEnum contentType,
                                  String contentTitle, String contentSummary, String contentCover, String contentUrl) {
        // 验证必需参数
        if (publisherId == null || publisherId <= 0) {
            throw new IllegalArgumentException("publisherId 不能为空且必须大于0");
        }
        if (contentId == null || contentId <= 0) {
            throw new IllegalArgumentException("contentId 不能为空且必须大于0");
        }
        if (contentType == null) {
            throw new IllegalArgumentException("contentType 不能为空");
        }

        // 验证字符串参数长度和格式
        validateStringParameter("contentTitle", contentTitle, 500, false);
        validateStringParameter("contentSummary", contentSummary, 1000, false);
        validateUrlParameter("contentCover", contentCover, 500, false);
        validateUrlParameter("contentUrl", contentUrl, 500, false);
    }

    /**
     * 验证字符串参数
     */
    private void validateStringParameter(String paramName, String value, int maxLength, boolean required) {
        if (required && (value == null || value.trim().isEmpty())) {
            throw new IllegalArgumentException(paramName + " 不能为空");
        }
        if (value != null) {
            if (value.length() > maxLength) {
                throw new IllegalArgumentException(paramName + " 长度不能超过" + maxLength + "字符");
            }
            // 检查是否包含危险字符
            if (containsDangerousCharacters(value)) {
                throw new IllegalArgumentException(paramName + " 包含非法字符");
            }
        }
    }

    /**
     * 验证URL参数
     */
    private void validateUrlParameter(String paramName, String url, int maxLength, boolean required) {
        validateStringParameter(paramName, url, maxLength, required);
        if (url != null && !url.trim().isEmpty()) {
            // 简单的URL格式验证
            if (!isValidUrl(url)) {
                throw new IllegalArgumentException(paramName + " URL格式不正确");
            }
        }
    }

    /**
     * 检查是否包含危险字符
     */
    private boolean containsDangerousCharacters(String value) {
        // 检查SQL注入和XSS攻击相关字符
        String[] dangerousPatterns = {"<script", "javascript:", "onload=", "onerror=", "'", "\"", ";", "--"};
        String lowerValue = value.toLowerCase();
        for (String pattern : dangerousPatterns) {
            if (lowerValue.contains(pattern)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 简单的URL格式验证
     */
    private boolean isValidUrl(String url) {
        try {
            new java.net.URL(url);
            return true;
        } catch (java.net.MalformedURLException e) {
            return false;
        }
    }

    /**
     * 生成任务ID
     * 基于发布者ID和内容ID生成唯一的任务ID，确保幂等性
     */
    private String generateTaskId(Long publisherId, Long contentId) {
        return String.format("content_publish_%s_%s", publisherId, contentId);
    }

    /**
     * 获取Redis分布式锁
     * 使用Redis SETNX命令实现分布式锁，增加锁值验证
     */
    private boolean acquireDistributedLock(String lockKey, long timeoutMs) {
        try {
            long startTime = System.currentTimeMillis();
            long expireTime = startTime + timeoutMs;
            // 使用实例ID和时间戳组成锁值，确保唯一性和可验证性
            String lockValue = instanceId + ":" + (System.currentTimeMillis() + REDIS_LOCK_TIMEOUT_MS);

            while (System.currentTimeMillis() < expireTime) {
                // 使用SET命令的NX和PX选项实现原子性的加锁和设置过期时间
                Boolean lockAcquired = stringRedisTemplate.opsForValue()
                    .setIfAbsent(lockKey, lockValue, java.time.Duration.ofMillis(REDIS_LOCK_TIMEOUT_MS));

                if (Boolean.TRUE.equals(lockAcquired)) {
                    log.debug("成功获取Redis分布式锁，lockKey: {}, lockValue: {}", lockKey, lockValue);
                    // 将锁值存储到ThreadLocal中，用于后续释放锁时验证
                    lockValueThreadLocal.set(lockValue);
                    return true;
                }

                // 等待一段时间后重试
                Thread.sleep(REDIS_LOCK_RETRY_INTERVAL_MS);
            }

            log.warn("获取Redis分布式锁超时，lockKey: {}, timeoutMs: {}", lockKey, timeoutMs);
            return false;

        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.warn("获取Redis分布式锁被中断，lockKey: {}", lockKey);
            return false;
        } catch (Exception e) {
            log.error("获取Redis分布式锁失败，lockKey: {}", lockKey, e);
            // Redis连接异常时的降级处理
            log.warn("Redis连接异常，尝试无锁模式继续处理，lockKey: {}", lockKey);
            return false;
        }
    }

    /**
     * 释放Redis分布式锁
     * 使用Lua脚本确保只有锁的持有者才能释放锁
     */
    private void releaseDistributedLock(String lockKey) {
        try {
            String lockValue = lockValueThreadLocal.get();
            if (lockValue == null) {
                log.warn("当前线程没有持有锁值，可能锁获取失败，lockKey: {}", lockKey);
                return;
            }

            // 使用Lua脚本确保原子性：只有当key存在且值匹配时才删除
            String luaScript = "if redis.call('get', KEYS[1]) == ARGV[1] then " +
                              "return redis.call('del', KEYS[1]) " +
                              "else " +
                              "return 0 " +
                              "end";

            Long result = stringRedisTemplate.execute(
                new org.springframework.data.redis.core.script.DefaultRedisScript<>(luaScript, Long.class),
                java.util.Collections.singletonList(lockKey),
                lockValue
            );

            if (result != null && result > 0) {
                log.debug("成功释放Redis分布式锁，lockKey: {}, lockValue: {}", lockKey, lockValue);
            } else {
                log.debug("Redis分布式锁不存在或锁值不匹配，lockKey: {}, lockValue: {}", lockKey, lockValue);
            }
        } catch (Exception e) {
            log.error("释放Redis分布式锁失败，lockKey: {}", lockKey, e);
        } finally {
            // 清理ThreadLocal，防止内存泄漏
            lockValueThreadLocal.remove();
        }
    }

    /**
     * 清理过期的Redis分布式锁
     * Redis的TTL机制会自动清理过期的key，这个方法主要用于监控和统计
     */
    public void cleanupExpiredLocks() {
        try {
            // Redis会自动清理过期的key，这里主要用于日志记录
            log.debug("Redis分布式锁由TTL机制自动清理，无需手动清理");
        } catch (Exception e) {
            log.error("检查Redis分布式锁状态时发生异常", e);
        }
    }

    /**
     * 获取当前系统状态信息
     */
    public java.util.Map<String, Object> getSystemStatus() {
        java.util.Map<String, Object> status = new java.util.HashMap<>();
        status.put("currentTaskCount", currentTaskCount.get());
        status.put("maxConcurrentTasks", MAX_CONCURRENT_TASKS);
        status.put("redisLockTimeoutMs", REDIS_LOCK_TIMEOUT_MS);
        status.put("redisLockRetryIntervalMs", REDIS_LOCK_RETRY_INTERVAL_MS);
        status.put("batchSize", BATCH_SIZE);
        return status;
    }

    private String getContentTypeDesc(ContentTypeEnum contentType) {
        return contentType != null ? contentType.getDesc() : "内容";
    }

    private String getCurrenttenantCode() {
        // TODO: 从上下文获取租户ID
        return "default";
    }

    private String getCurrentOperator() {
        // TODO: 从上下文获取当前操作人
        return "system";
    }

    /**
     * 清理ThreadLocal资源，防止内存泄漏
     * 应该在请求结束时调用
     */
    public void cleanupThreadLocal() {
        try {
            lockValueThreadLocal.remove();
            log.debug("清理ThreadLocal资源完成");
        } catch (Exception e) {
            log.error("清理ThreadLocal资源时发生异常", e);
        }
    }
}
